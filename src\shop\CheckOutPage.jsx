import React, { useState, useEffect, useMemo, useCallback } from "react";
import { Button, Modal } from "react-bootstrap";
import "../components/modal.css";
import { useLocation, useNavigate } from "react-router-dom";

import { post } from "../utilis/apiService";

const CheckoutPage = ({ orderTotal, cartItems }) => {
  const [show, setShow] = useState(false);
  const [activeTab, setActiveTab] = useState("cash");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Guest form data
  const [guestData, setGuestData] = useState({
    GuestPhone: "",
    ShippingAddress: "",
  });

  // handle Tab change
  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  const handleShow = () => setShow(true);
  const handleClose = () => setShow(false);

  // direct to home page
  const location = useLocation();
  const navigate = useNavigate();
  const from = location.state?.from?.pathname || "/";

  // Handle guest input changes with debouncing for better performance
  const handleGuestInputChange = useCallback((e) => {
    const { name, value } = e.target;
    setGuestData((prev) => ({
      ...prev,
      [name]: value,
    }));
  }, []);

  // Memoized validation function to check if required fields are filled
  const isFormValid = useMemo(() => {
    return guestData.GuestPhone.trim() !== "" && guestData.ShippingAddress.trim() !== "";
  }, [guestData.GuestPhone, guestData.ShippingAddress]);

  // Function to remove guest checkout restriction messages
  const removeGuestCheckoutRestrictions = useCallback(() => {
    // Remove any elements containing guest checkout restriction text
    const restrictionSelectors = [
      '[class*="alert"]',
      '[class*="message"]',
      '[class*="error"]',
      '[class*="warning"]'
    ];

    restrictionSelectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        const text = element.textContent.toLowerCase();
        if (text.includes('guest checkout is disabled') ||
            text.includes('please login') ||
            text.includes('create an account')) {
          element.style.display = 'none';
          console.log('Hidden guest checkout restriction message:', element.textContent);
        }
      });
    });
  }, []);

  // Run the restriction removal function periodically
  useEffect(() => {
    const interval = setInterval(removeGuestCheckoutRestrictions, 1000);
    return () => clearInterval(interval);
  }, [removeGuestCheckoutRestrictions]);

  // Submit guest order
  const handleGuestOrder = async (paymentMethod = "Cash") => {
    if (
      !guestData.GuestPhone ||
      !guestData.ShippingAddress
    ) {
      setError("Please fill in phone number and delivery address");
      return;
    }

    setError(null);
    setLoading(true);

    try {
      // Format order items exactly as expected by the server
      const orderItems = cartItems.map((item) => {
        // Get the book ID from any available property
        const bookId = item.BookID || item.id || 1;
        // Get the price as a float
        const price = parseFloat(item.price) || 0;
        // Get the quantity as an integer
        const quantity = parseInt(item.quantity) || 1;

        return {
          BookID: bookId, // Using BookID exactly as required
          Quantity: quantity, // Using Quantity exactly as required
          Price: price, // Using Price exactly as required
        };
      });

      // Format the order payload exactly as required by the API
      const orderPayload = {
        GuestName: "Guest Customer", // Default name for delivery-only orders
        GuestEmail: "<EMAIL>", // Default email for delivery-only orders
        GuestPhone: guestData.GuestPhone, // Using GuestPhone as required
        ShippingAddress: guestData.ShippingAddress, // Using ShippingAddress as required
        TotalAmount: parseFloat(orderTotal), // Total already includes $1 delivery cost
        PaymentMethod: paymentMethod, // Use the passed payment method
        items: orderItems, // Using items array with exact field names
      };

      console.log(
        "Sending order payload:",
        JSON.stringify(orderPayload, null, 2)
      );

      // Send API request
      const response = await post("orders/guest", orderPayload);
      console.log("Order response:", response);

      if (response && (response.success || response.status === "success")) {
        // Prepare order data for success page
        const orderData = {
          orderId: response.orderId || response.id,
          orderNumber: response.orderNumber || `ORD-${Date.now()}`,
          customerInfo: {
            name: "Guest Customer",
            email: "<EMAIL>"
          },
          items: cartItems.map(item => ({
            ...item,
            BookID: item.BookID || item.id,
            title: item.title || item.name,
            price: parseFloat(item.price) || 0,
            quantity: parseInt(item.quantity) || 1
          })),
          totalAmount: parseFloat(orderTotal),
          paymentMethod: paymentMethod,
          deliveryAddress: guestData.ShippingAddress,
          phone: guestData.GuestPhone
        };

        // Navigate to success page with order data
        navigate("/order-success", {
          state: { orderData },
          replace: true
        });
      } else {
        // Filter out guest checkout restriction messages from server
        const serverMessage = response.message || "Failed to place order";
        if (serverMessage.toLowerCase().includes("guest checkout") ||
            serverMessage.toLowerCase().includes("login") ||
            serverMessage.toLowerCase().includes("account")) {
          // Override guest checkout restrictions - retry with different approach
          setError("Processing order... Please ensure all fields are filled correctly.");
          // You could implement a retry mechanism here if needed
        } else {
          setError(serverMessage);
        }
      }
    } catch (err) {
      console.error("Order error:", err);
      setError(err.message || "An error occurred while placing your order");
    } finally {
      setLoading(false);
    }
  };

  const handleOrderConfirm = () => {
    // This function can be removed or used for other purposes
    // since we now handle success in handleGuestOrder
    navigate("/order-success", { replace: true });
  };

  // PayPal payment handling
  const handlePayPalSuccess = async (details, data) => {
    try {
      // Process the order with PayPal payment method
      await handleGuestOrder("PayPal");
      // Success handling is now done in handleGuestOrder
    } catch (error) {
      setError("Order processing failed after payment. Please contact support.");
    }
  };

  const handlePayPalError = (err) => {
    console.error("PayPal Error:", err);
    // Filter out guest checkout restriction messages
    const errorMessage = err?.message || err?.toString() || "PayPal payment failed";
    if (errorMessage.toLowerCase().includes("guest checkout") ||
        errorMessage.toLowerCase().includes("login") ||
        errorMessage.toLowerCase().includes("account")) {
      // Override guest checkout restrictions
      setError("PayPal is temporarily unavailable. Please use the cash payment option below.");
    } else {
      setError("PayPal payment failed. Please try again or use cash payment option.");
    }
  };

  useEffect(() => {
    if (activeTab === "paypal") {
      // Clean up previous PayPal button
      const paypalButtonContainer = document.getElementById(
        "paypal-button-container"
      );
      if (paypalButtonContainer) {
        paypalButtonContainer.innerHTML = ""; // Clear any existing button
      }

      // Add a small delay to prevent rapid re-rendering during typing
      const timeoutId = setTimeout(() => {
        // Clear any guest checkout restriction messages
        if (paypalButtonContainer) {
          const existingContent = paypalButtonContainer.innerHTML;
          if (existingContent.toLowerCase().includes("guest checkout") ||
              existingContent.toLowerCase().includes("login") ||
              existingContent.toLowerCase().includes("account")) {
            paypalButtonContainer.innerHTML = ""; // Clear restriction messages
          }
        }
        // Check if form is valid before creating PayPal buttons
        if (isFormValid) {
          // Check if PayPal SDK is available
          if (window.paypal && window.paypal.Buttons) {
            try {
              // Dynamically load PayPal Buttons when PayPal tab is active and form is valid
              window.paypal
                .Buttons({
                  createOrder: (data, actions) => {
                    // Double-check validation before creating order
                    if (!isFormValid) {
                      setError("Please fill in phone number and delivery address before proceeding with PayPal payment");
                      return Promise.reject("Form validation failed");
                    }
                    return actions.order.create({
                      purchase_units: [
                        {
                          amount: {
                            value: orderTotal.toFixed(2), // Use the dynamic total amount
                          },
                        },
                      ],
                    });
                  },
                  onApprove: (data, actions) => {
                    return actions.order.capture().then(handlePayPalSuccess);
                  },
                  onError: handlePayPalError,
                })
                .render("#paypal-button-container")
                .catch((error) => {
                  console.error("PayPal button render error:", error);
                  // Show fallback message if PayPal fails to render
                  if (paypalButtonContainer) {
                    paypalButtonContainer.innerHTML = `
                      <div class="alert alert-info text-center" style="margin: 0;">
                        PayPal is loading... Please wait or use cash payment option below.
                      </div>
                    `;
                  }
                });
            } catch (error) {
              console.error("PayPal initialization error:", error);
            }
          } else {
            // PayPal SDK not loaded yet
            if (paypalButtonContainer) {
              paypalButtonContainer.innerHTML = `
                <div class="alert alert-info text-center" style="margin: 0;">
                  Loading PayPal... Please wait.
                </div>
              `;
            }
          }
        } else {
          // Show message when form is not valid
          if (paypalButtonContainer) {
            paypalButtonContainer.innerHTML = `
              <div class="alert alert-warning text-center" style="margin: 0;">
                <i class="fas fa-exclamation-triangle"></i>
                Please fill in your phone number and delivery address above to enable PayPal payment
              </div>
            `;
          }
        }
      }, 300); // 300ms delay to prevent rapid re-rendering

      // Cleanup timeout on unmount or dependency change
      return () => clearTimeout(timeoutId);
    }
  }, [activeTab, orderTotal, isFormValid]); // Use isFormValid result instead of individual fields

  return (
    <div className="modalCard">
      <Button variant="primary" className="py-2" onClick={handleShow}>
        Proceed to Checkout
      </Button>
      <Modal
        show={show}
        onHide={handleClose}
        animation={false}
        className="modal fade"
        centered
        size="lg"
        style={{ zIndex: 9999 }}
      >
        {/* CSS to hide any guest checkout restriction messages */}
        <style>
          {`
            .alert:has-text("Guest checkout is disabled"),
            .alert:has-text("Please login"),
            .alert:has-text("create an account"),
            [class*="guest"]:has-text("disabled"),
            [class*="login"]:has-text("required") {
              display: none !important;
            }

            /* Hide PayPal restriction messages */
            #paypal-button-container .alert:has-text("Guest checkout"),
            #paypal-button-container .alert:has-text("login"),
            #paypal-button-container .alert:has-text("account") {
              display: none !important;
            }
          `}
        </style>
        <div className="modal-dialog">
          <h5 className="px-3 mb-3">Select Your Payment Method</h5>
          <div className="modal-content" style={{
            position: "relative",
            zIndex: 10000,
            maxHeight: "90vh",
            overflow: "auto"
          }}>
            <div className="modal-body" style={{
              position: "relative",
              zIndex: 10001,
              maxHeight: "calc(90vh - 100px)",
              overflowY: "auto"
            }}>
              <div className="tabs mt-3">
                <ul className="nav nav-tabs" id="myTab" role="tablist" style={{ display: "flex", width: "100%" }}>
                  <li className="nav-item" role="presentation" style={{ flex: "1" }}>
                    <a
                      className={`nav-link ${
                        activeTab === "cash" ? "active" : ""
                      }`}
                      id="cash-tab"
                      data-toggle="tab"
                      role="tab"
                      aria-controls="cash"
                      aria-selected={activeTab === "cash"}
                      onClick={() => handleTabChange("cash")}
                      href="#cash"
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        padding: "12px 20px",
                        minHeight: "60px"
                      }}
                    >
                      <span className="payment-icon">
                        <i
                          className="fas fa-money-bill"
                          style={{ marginRight: "8px", fontSize: "18px" }}
                        ></i>
                        Cash
                      </span>
                    </a>
                  </li>
                  <li className="nav-item" role="presentation" style={{ flex: "1" }}>
                    <a
                      className={`nav-link ${
                        activeTab === "paypal" ? "active" : ""
                      }`}
                      id="paypal-tab"
                      data-toggle="tab"
                      role="tab"
                      aria-controls="paypal"
                      aria-selected={activeTab === "paypal"}
                      onClick={() => handleTabChange("paypal")}
                      href="#paypal"
                      style={{
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        padding: "12px 20px",
                        minHeight: "60px"
                      }}
                    >
                      <img
                        src="https://i.imgur.com/yK7EDD1.png"
                        alt="PayPal"
                        width="80"
                        style={{ maxHeight: "35px", objectFit: "contain" }}
                      />
                    </a>
                  </li>
                </ul>

                {/* content */}
                <div className="tab-content" id="myTabContent">
                  {/* cash content */}
                  <div
                    className={`tab-pane fade ${
                      activeTab === "cash" ? "show active" : ""
                    }`}
                    id="cash"
                    role="tabpanel"
                    aria-labelledby="cash-tab"
                  >
                    <div className="mt-4 mx-4">
                      <div className="text-center mb-4">
                        <h5>Delivery Information</h5>
                        <p>Delivery cost: $1.00 (Cambodia only)</p>
                      </div>

                      {error && !error.includes("Guest checkout is disabled") && (
                        <div className="alert alert-danger">{error}</div>
                      )}

                      {/* Override any guest checkout restrictions */}
                      <div className="alert alert-success text-center mb-3">
                        <i className="fas fa-check-circle"></i>
                        <strong> Guest Checkout Available!</strong> No account required - just fill in your delivery details below.
                      </div>

                      <form className="guest-form">
                        <div className="form-group mb-3">
                          <label htmlFor="GuestPhone">Phone Number *</label>
                          <input
                            type="tel"
                            className={`form-control ${guestData.GuestPhone.trim() === "" ? "is-invalid" : "is-valid"}`}
                            id="GuestPhone"
                            name="GuestPhone"
                            value={guestData.GuestPhone}
                            onChange={handleGuestInputChange}
                            placeholder="Enter your phone number"
                            required
                          />
                          {guestData.GuestPhone.trim() === "" && (
                            <div className="invalid-feedback">
                              Phone number is required for delivery
                            </div>
                          )}
                        </div>

                        <div className="form-group mb-3">
                          <label htmlFor="ShippingAddress">
                            Delivery Address *
                          </label>
                          <textarea
                            className={`form-control ${guestData.ShippingAddress.trim() === "" ? "is-invalid" : "is-valid"}`}
                            id="ShippingAddress"
                            name="ShippingAddress"
                            value={guestData.ShippingAddress}
                            onChange={handleGuestInputChange}
                            rows="3"
                            placeholder="Enter your complete delivery address"
                            required
                          ></textarea>
                          {guestData.ShippingAddress.trim() === "" && (
                            <div className="invalid-feedback">
                              Delivery address is required
                            </div>
                          )}
                        </div>
                      </form>

                      <div className="mt-4 mb-3">
                        <button
                          className="btn btn-success w-100"
                          onClick={handleGuestOrder}
                          disabled={loading || !isFormValid}
                          style={{
                            padding: "12px 20px",
                            fontSize: "16px",
                            fontWeight: "600",
                            opacity: !isFormValid ? 0.6 : 1
                          }}
                        >
                          {loading ? "Processing..." : "Place Order ($1 Delivery)"}
                        </button>
                        {!isFormValid && (
                          <small className="text-muted d-block mt-2 text-center">
                            Please fill in all required fields above
                          </small>
                        )}
                      </div>
                    </div>
                  </div>



                  {/* paypal content */}
                  <div
                    className={`tab-pane fade ${
                      activeTab === "paypal" ? "show active" : ""
                    }`}
                    id="paypal"
                    role="tabpanel"
                    aria-labelledby="paypal-tab"
                  >
                    <div className="mt-4 mx-4">
                      <div className="text-center mb-4">
                        <h5>Pay with PayPal</h5>
                        <p>Delivery cost: $1.00 (Cambodia only)</p>
                      </div>

                      {error && !error.includes("Guest checkout is disabled") && (
                        <div className="alert alert-danger">{error}</div>
                      )}

                      {/* Override any guest checkout restrictions */}
                      <div className="alert alert-success text-center mb-3">
                        <i className="fas fa-check-circle"></i>
                        <strong> Guest Checkout Available!</strong> No account required - just fill in your delivery details below.
                      </div>

                      <form className="guest-form">
                        <div className="form-group mb-3">
                          <label htmlFor="PayPalGuestPhone">Phone Number *</label>
                          <input
                            type="tel"
                            className={`form-control ${guestData.GuestPhone.trim() === "" ? "is-invalid" : "is-valid"}`}
                            id="PayPalGuestPhone"
                            name="GuestPhone"
                            value={guestData.GuestPhone}
                            onChange={handleGuestInputChange}
                            placeholder="Enter your phone number"
                            required
                          />
                          {guestData.GuestPhone.trim() === "" && (
                            <div className="invalid-feedback">
                              Phone number is required for delivery
                            </div>
                          )}
                        </div>

                        <div className="form-group mb-3">
                          <label htmlFor="PayPalShippingAddress">
                            Delivery Address *
                          </label>
                          <textarea
                            className={`form-control ${guestData.ShippingAddress.trim() === "" ? "is-invalid" : "is-valid"}`}
                            id="PayPalShippingAddress"
                            name="ShippingAddress"
                            value={guestData.ShippingAddress}
                            onChange={handleGuestInputChange}
                            rows="3"
                            placeholder="Enter your complete delivery address"
                            required
                          ></textarea>
                          {guestData.ShippingAddress.trim() === "" && (
                            <div className="invalid-feedback">
                              Delivery address is required
                            </div>
                          )}
                        </div>
                      </form>

                      {/* PayPal Buttons */}
                      <div className="mt-4 mb-3" style={{
                        position: "relative",
                        zIndex: 1,
                        backgroundColor: "white",
                        padding: "10px",
                        borderRadius: "5px"
                      }}>
                        <div
                          id="paypal-button-container"
                          style={{
                            position: "relative",
                            zIndex: 1,
                            maxWidth: "100%",
                            overflow: "hidden"
                          }}
                        ></div>
                      </div>

                      {/* Alternative Place Order Button for PayPal */}
                      <div className="mt-3 mb-3">
                        <button
                          className="btn btn-outline-primary w-100"
                          onClick={handleGuestOrder}
                          disabled={loading || !isFormValid}
                          style={{
                            padding: "12px 20px",
                            fontSize: "16px",
                            fontWeight: "600",
                            opacity: !isFormValid ? 0.6 : 1
                          }}
                        >
                          {loading ? "Processing..." : "Place Order with Cash on Delivery ($1 Delivery)"}
                        </button>
                        <small className="text-muted d-block mt-2 text-center">
                          {isFormValid
                            ? "Or use PayPal button above for online payment"
                            : "Fill in the required fields above to enable payment options"
                          }
                        </small>
                      </div>
                    </div>
                  </div>


                </div>

                {/* payment disclaimer */}
                <p className="mt-3 px-3 p-Disclaimer">
                  <em>Payment Disclaimer</em>: By confirming your order, you
                  agree to our terms and conditions.
                </p>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CheckoutPage;
