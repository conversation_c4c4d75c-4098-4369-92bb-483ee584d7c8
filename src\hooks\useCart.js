import { useState, useEffect, useCallback } from 'react';
import { 
  getCartItems, 
  getCartItemCount, 
  addToCart as addToCartUtil, 
  removeFromCart as removeFromCartUtil,
  updateCartItemQuantity as updateCartItemQuantityUtil,
  clearCart as clearCartUtil,
  getCartTotal
} from '../utilis/cartUtils';

/**
 * Custom hook for cart management
 * Provides cart state and operations with real-time updates
 */
export const useCart = () => {
  const [cartItems, setCartItems] = useState([]);
  const [cartCount, setCartCount] = useState(0);
  const [cartTotal, setCartTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Load cart data from localStorage
  const loadCartData = useCallback(() => {
    const items = getCartItems();
    const count = getCartItemCount();
    const total = getCartTotal();

    setCartItems(items);
    setCartCount(count);
    setCartTotal(total);

    // Dispatch custom event to notify other components
    window.dispatchEvent(new CustomEvent('cartUpdated', {
      detail: { items, count, total }
    }));
  }, []);

  // Initialize cart data on mount and listen for storage changes
  useEffect(() => {
    loadCartData();

    // Listen for localStorage changes (from other tabs/windows)
    const handleStorageChange = (e) => {
      if (e.key === 'cart') {
        loadCartData();
      }
    };

    // Listen for custom cart events (from same window)
    const handleCartEvent = () => {
      loadCartData();
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('cartCleared', handleCartEvent);
    window.addEventListener('cartUpdated', handleCartEvent);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('cartCleared', handleCartEvent);
      window.removeEventListener('cartUpdated', handleCartEvent);
    };
  }, [loadCartData]);

  // Listen for storage changes (cart updates from other tabs/components)
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'cart') {
        loadCartData();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [loadCartData]);

  // Add item to cart
  const addToCart = useCallback(async (product, quantity = 1) => {
    setIsLoading(true);
    try {
      const success = addToCartUtil(product, quantity);
      if (success) {
        loadCartData(); // Refresh cart data

        // Dispatch toast event
        window.dispatchEvent(new CustomEvent('showToast', {
          detail: {
            message: `${product.Title || product.title || 'Item'} added to cart!`,
            type: 'success'
          }
        }));

        return { success: true, message: 'Item added to cart successfully!' };
      } else {
        return { success: false, message: 'Failed to add item to cart' };
      }
    } catch (error) {
      console.error('Error in addToCart:', error);
      return { success: false, message: 'An error occurred while adding to cart' };
    } finally {
      setIsLoading(false);
    }
  }, [loadCartData]);

  // Remove item from cart
  const removeFromCart = useCallback(async (itemId) => {
    setIsLoading(true);
    try {
      // Get item name before removing
      const items = getCartItems();
      const itemToRemove = items.find(item => item.id === itemId);
      const itemName = itemToRemove?.name || 'Item';

      const success = removeFromCartUtil(itemId);
      if (success) {
        loadCartData(); // Refresh cart data

        // Dispatch toast event
        window.dispatchEvent(new CustomEvent('showToast', {
          detail: {
            message: `${itemName} removed from cart`,
            type: 'success'
          }
        }));

        return { success: true, message: 'Item removed from cart' };
      } else {
        return { success: false, message: 'Failed to remove item from cart' };
      }
    } catch (error) {
      console.error('Error in removeFromCart:', error);
      return { success: false, message: 'An error occurred while removing from cart' };
    } finally {
      setIsLoading(false);
    }
  }, [loadCartData]);

  // Update item quantity
  const updateItemQuantity = useCallback(async (itemId, newQuantity) => {
    setIsLoading(true);
    try {
      const success = updateCartItemQuantityUtil(itemId, newQuantity);
      if (success) {
        loadCartData(); // Refresh cart data
        return { success: true, message: 'Quantity updated successfully' };
      } else {
        return { success: false, message: 'Failed to update quantity' };
      }
    } catch (error) {
      console.error('Error in updateItemQuantity:', error);
      return { success: false, message: 'An error occurred while updating quantity' };
    } finally {
      setIsLoading(false);
    }
  }, [loadCartData]);

  // Clear entire cart
  const clearCart = useCallback(async () => {
    setIsLoading(true);
    try {
      const success = clearCartUtil();
      if (success) {
        loadCartData(); // Refresh cart data
        return { success: true, message: 'Cart cleared successfully' };
      } else {
        return { success: false, message: 'Failed to clear cart' };
      }
    } catch (error) {
      console.error('Error in clearCart:', error);
      return { success: false, message: 'An error occurred while clearing cart' };
    } finally {
      setIsLoading(false);
    }
  }, [loadCartData]);

  // Check if item is in cart
  const isInCart = useCallback((productId) => {
    return cartItems.some(item => item.id === productId);
  }, [cartItems]);

  // Get item quantity in cart
  const getItemQuantity = useCallback((productId) => {
    const item = cartItems.find(item => item.id === productId);
    return item ? item.quantity : 0;
  }, [cartItems]);

  return {
    // State
    cartItems,
    cartCount,
    cartTotal,
    isLoading,
    
    // Actions
    addToCart,
    removeFromCart,
    updateItemQuantity,
    clearCart,
    
    // Utilities
    isInCart,
    getItemQuantity,
    refreshCart: loadCartData,
  };
};
